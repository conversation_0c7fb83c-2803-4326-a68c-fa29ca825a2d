const user = require('./userEntity');
const company = require('./companyEntity');
const supplier = require('./supplierEntity');
const department = require('./departmentEntity');
const project = require('./projectEntity');
const filter = require('./filterEntity');
const item = require('./itemEntity');
const auditLog = require('./auditLogEntity');
const nonOfmItem = require('./nonOfmItemEntity');
const ofmItem = require('./ofmItemEntity');
const trade = require('./tradeEntity');
const requisition = require('./requisitionEntity');
const requisitionItemList = require('./requisitionItemListEntity');
const approval = require('./approvalEntity');
const tomItem = require('./tomItemEntity');
const deliveryReceipt = require('./deliveryReceiptEntity');
const canvass = require('./canvassEntity');
const attachment = require('./attachmentEntity');
const deliveryReceiptItem = require('./deliveryReceiptItemEntity');
const note = require('./noteEntity');
const history = require('./historyEntity');
const leave = require('./leaveEntity');
const steelbars = require('./steelbarsEntity');
const warranty = require('./warrantyEntity');
const purchaseOrder = require('./purchaseOrderEntity');
const requisitionHistory = require('./requisitionHistoryEntity');
const rsPaymentRequest = require('./rsPaymentRequestEntity');
const requestHistory = require('./requestHistoryEntity');
const nonRequisition = require('./nonRequisitionEntity');
const invoiceReport = require('./invoiceReportEntity');
const forceClose = require('./forceCloseEntity');

module.exports = {
  user,
  company,
  supplier,
  department,
  project,
  item,
  filter,
  auditLog,
  nonOfmItem,
  ofmItem,
  trade,
  requisition,
  requisitionItemList,
  approval,
  tomItem,
  deliveryReceipt,
  canvass,
  attachment,
  deliveryReceiptItem,
  note,
  history,
  leave,
  steelbars,
  warranty,
  purchaseOrder,
  requisitionHistory,
  rsPaymentRequest,
  requestHistory,
  nonRequisition,
  invoiceReport,
  forceClose,
};
