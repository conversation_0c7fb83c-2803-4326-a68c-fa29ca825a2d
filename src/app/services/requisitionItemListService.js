class RequisitionItemListService {
  constructor ({
    clientErrors,
    requisitionItemListRepository,
    requisitionRepository,
    requisitionApproverRepository,
  }) {
    this.clientErrors = clientErrors;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.requisitionRepository = requisitionRepository;
    this.requisitionApproverRepository = requisitionApproverRepository;
  }

  async validateExistingRSItems(rsItemIds = [], requisitionId) {
    const uniqueRsItemsIds = [...new Set(rsItemIds)];

    const { data: rsItems } =
      await this.requisitionItemListRepository.findRSItemByIds(
        { rsItemIds: uniqueRsItemsIds, requisitionId },
        {
          attributes: ['id'],
          paginate: false,
          paranoid: true,
        },
      );

    const missingRSItemIds = uniqueRsItemsIds.filter(
      (id) => !rsItems.find((item) => item.id === id),
    );

    if (missingRSItemIds.length > 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Unable to find RS items with ID(s): ${missingRSItemIds}`,
      });
    }

    return rsItems;
  }

  async deleteRequisitionItemList({
    params,
    userFromToken,
  }) {
    const { requisitionId, itemId } = params;

    // const requisitionItemList =
    //   await this.requisitionItemListRepository.getById(itemId);
    const {
      data: requisitionItemList,
      total: requisitionItemListTotal,
    } =
      await this.requisitionItemListRepository.findAll({
        where: { requisitionId },
        paginate: false,
      });

    const { rsItems, itemRSId } = this.#rsItems(requisitionItemList, itemId);

    if (requisitionItemList.length === 0 || !rsItems.includes(parseInt(itemId))
    ) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition item list with id ${itemId} not found`,
      });
    }

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id ${requisitionId} not found`,
      });
    }

    if (parseInt(itemRSId) !== parseInt(requisitionId)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Requisition item list with id ${itemId} does not belong to requisition with id ${requisitionId}`,
      });
    }

    const isRsApprover = await this.requisitionApproverRepository.findOne({
      where: {
        requisitionId,
        approverId: userFromToken.id,
      },
    });

    if (!isRsApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `User with id ${userFromToken.id} is not an RS approver for requisition with id ${requisitionId}`,
      });
    }

    if (requisitionItemListTotal === 1) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Cannot delete the last item in the requisition item list`,
      });
    }

    await this.requisitionItemListRepository.destroyById(itemId);

    return requisitionItemList;
  }

  #rsItems(requisitionItemList, itemId) {
    const rsItems = [];
    let itemRSId = null;

    for (const item of requisitionItemList) {
      rsItems.push(item.id);

      if (item.id === parseInt(itemId)) {
        itemRSId = item.requisitionId;
      }
    }

    return { rsItems, itemRSId };
  }
}

module.exports = RequisitionItemListService;
