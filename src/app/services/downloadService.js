const converter = require('json-2-csv');
class LeaveService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      leaveRepository,
      fastify,
      requisitionRepository,
      nonRequisitionRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.leaveRepository = leaveRepository;
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.requisitionRepository = requisitionRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
  }

  #generateNewLine() {
    return '\r\n';
  }

  #generateDateLine() {
    const formattedDate = this.utils.formatDateYYYYMMDD();
    const dateLine = { [`Extract as of ${formattedDate}`]: '' };

    return converter.json2csv(dateLine);
  }

  #formatExcelFile(...lines) {
    return lines.join('');
  }

  async downloadDashboardExcel(payload) {
    const {
      limit = 1_000_000,
      requestType = 'all',
      userId,
      ...options
    } = payload;
    const data = await this.requisitionRepository.getAllRequisitionsV2({
      limit,
      userId,
      ...options,
    });

    const transformData = [];

    data[requestType]?.forEach((data) =>
      transformData.push({
        [`Ref No`]: `${data.ref_number}`,
        Type: `${data.doc_type}`,
        Requester: `${data.requestor_name}`,
        Company: `${data.company_name}`,
        Department: `${data.department_name || '---'}`,
        Project: `${data.project_name || '---'}`,
        ['Last Updated']: `${this.utils.formatToMonthDayYear(data.updated_at)}`,
        Status: data.status.replaceAll('_', ' ').toUpperCase(),
      }),
    );

    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }

  generateExcelFile(data) {
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(data),
    );
  }
}

module.exports = LeaveService;
