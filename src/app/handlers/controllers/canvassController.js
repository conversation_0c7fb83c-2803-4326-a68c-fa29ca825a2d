class Canvass {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      constants,
      clientErrors,
      userService,
      noteService,
      projectService,
      commentService,
      companyService,
      canvassService,
      supplierService,
      templateService,
      attachmentService,
      requisitionService,
      canvassItemService,
      notificationService,
      purchaseOrderService,
      requisitionItemListService,
      canvassItemSupplierRepository,
      requisitionCanvassHistoryService,
      canvassItemRepository,
      canvassApproverRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.constants = constants;
    this.userService = userService;
    this.noteService = noteService;
    this.clientErrors = clientErrors;
    this.projectService = projectService;
    this.commentService = commentService;
    this.companyService = companyService;
    this.canvassService = canvassService;
    this.templateService = templateService;
    this.supplierService = supplierService;
    this.attachmentService = attachmentService;
    this.canvassItemService = canvassItemService;
    this.notificationService = notificationService;
    this.purchaseOrderService = purchaseOrderService;
    this.requisitionService = requisitionService;
    this.requisitionItemListService = requisitionItemListService;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
    this.requisitionCanvassHistoryService = requisitionCanvassHistoryService;
    this.canvassItemRepository = canvassItemRepository;
    this.canvassApproverRepository = canvassApproverRepository;
  }

  async getAllCanvass(request, reply) {
    const { requisitionId } = request.params;
    const payload = {
      ...request.query,
      requisitionId,
    };

    const canvassList = await this.canvassService.getAllCanvass(payload);
    return reply.status(200).send(canvassList);
  }

  async getCanvass(request, reply) {
    const { id: canvassId } = request.params;
    const canvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    return reply.status(200).send(canvass);
  }

  async createCanvass(request, reply) {
    const { userFromToken, body } = request;
    const { attachments, ...restBody } = { ...body };
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const { SUPPLIER_TYPE } = this.constants.canvass;
    const { MODELS } = this.constants.attachment;
    const { COMMENT_TYPES, USER_TYPES } = this.constants.note;

    const parsedBody = this.utils.parseDomain(
      this.entities.canvass.createCanvassSchema,
      restBody,
    );

    /* Initial validation for canvass creation/update */
    const requisition = await this.canvassService.canvassCreateValidation({
      creatorId: userFromToken.id,
      isDraft: parsedBody.isDraft,
      requisitionId: parsedBody.requisitionId,
      canvassId: parsedBody.id,
    });

    /* Validate RS Item Ids if exist */
    const requisitionItemListIds = [
      ...(parsedBody.addItems?.map((item) => item.requisitionItemListId) || []),
      ...(parsedBody.updateItems?.map((item) => item.requisitionItemListId) ||
        []),
    ];

    if (requisitionItemListIds.length > 0) {
      await this.requisitionItemListService.validateExistingRSItems(
        requisitionItemListIds,
        requisition.id,
      );
    }

    /* Group Suppliers by supplier type */
    const groupedBySupplierType = this.canvassService.groupSuppliersByType([
      ...(parsedBody.addItems || []),
      ...(parsedBody.updateItems || []),
    ]);

    /* Validate suppliers for each group */
    for (const [supplierType, suppliers] of Object.entries(
      groupedBySupplierType,
    )) {
      const supplierIds = suppliers
        .filter((supplier) => supplier?.supplierId)
        .map((supplier) => supplier.supplierId);

      if (supplierIds.length > 0) {
        if (supplierType === SUPPLIER_TYPE.COMPANY) {
          await this.companyService.validateCompany({
            companyIds: supplierIds,
          });
        } else if (supplierType === SUPPLIER_TYPE.PROJECT) {
          await this.projectService.validateProjects({
            projectIds: supplierIds,
          });
        } else {
          await this.supplierService.validateSuppliers({ supplierIds });
        }
      }
    }

    const transaction = await this.db.sequelize.transaction();

    try {
      const createdCanvass = await this.canvassService.createCanvass({
        ...parsedBody,
        transaction,
        assignedTo: requisition.assignedTo,
        canvassType: requisition.type,
        userFromToken,
      });

      if (createdCanvass.hasOverCanvassed) {
        throw this.clientErrors.CUSTOM_ERROR({
          message:
            'One or more items have been requested in excess of available or allowable quantity.',
          description: createdCanvass.overCanvassedItems,
          errorCode: 'ITEM_OVER_CANVASS',
          status: 422,
        });
      }

      if (attachments?.length) {
        await this.attachmentService.createAttachments({
          attachments,
          transaction,
          model: MODELS.CANVASS,
          parentPath: MODELS.CANVASS,
          userId: userFromToken.id,
          modelId: createdCanvass.id,
        });
      }

      if (parsedBody.notes) {
        await this.noteService.createNote({
          model: MODELS.CANVASS,
          modelId: createdCanvass.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.REQUESTOR,
          commentType: COMMENT_TYPES.NOTE,
          note: parsedBody.notes,
        });
      }

      if (createdCanvass.isSupplierUpdated) {
        await this.notificationService.sendNotification({
          transaction,
          senderId: userFromToken.id,
          type: NOTIFICATION_TYPES.CANVASS,
          title: 'Supplier Canvass Updated',
          message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the List of Suppliers for the Canvass Sheet of your Request. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
          recipientUserIds: [userFromToken.id, requisition.createdBy],
          metaData: {
            addedBy: userFromToken.id,
            canvassId: createdCanvass.id,
            requisitionId: requisition.id,
          },
        });
      }

      await this.requisitionCanvassHistoryService.createEntries({
        canvassRequisitionId: createdCanvass.id,
        transaction,
        isDraft: parsedBody.isDraft,
      });

      await transaction.commit();
      return reply.status(200).send({
        id: createdCanvass.id,
        message: 'Canvass successfully created',
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getCanvassItems(request, reply) {
    const { id: canvassId } = request.params;
    const { CANVASS_STATUS } = this.constants.canvass;

    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    const canvassItemList = await this.canvassItemService.getAllCanvassItems({
      query: request.query,
      canvassId: existingCanvass.id,
    });

    if (
      existingCanvass.status === CANVASS_STATUS.APPROVED &&
      canvassItemList.total
    ) {
      const updatedItemList = canvassItemList.data.map((item) => {
        if (!item.suppliers.length) return item;

        const approvedQty = item.suppliers.reduce((acc, supplier) => {
          if (!supplier.isSelected) return acc;

          return parseFloat((acc + parseFloat(supplier.quantity)).toFixed(3));
        }, 0);

        return { ...item, approvedQty };
      });

      return reply
        .status(200)
        .send({ ...canvassItemList, data: updatedItemList });
    }

    return reply.status(200).send(canvassItemList);
  }

  async getCanvassAttachments(request, reply) {
    const { id: canvassId } = request.params;
    const { attachmentDateFrom, attachmentDateTo, ...restFilters } =
      request.query;
    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    const attachments = await this.attachmentService.getAttachments({
      model: 'canvass',
      attachmentDateTo,
      attachmentDateFrom,
      modelId: existingCanvass.id,
      filters: restFilters,
    });

    return reply.status(200).send(attachments);
  }

  async getCanvassSupplierAttachments(request, reply) {
    const { id: canvassSupplierId } = request.params;
    const { MODELS } = this.constants.attachment;
    const { attachmentDateFrom, attachmentDateTo, ...restFilters } =
      request.query;

    const existingCanvassSupplier =
      await this.canvassItemSupplierRepository.getById(
        parseInt(canvassSupplierId),
      );

    if (!existingCanvassSupplier) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass item supplier not found',
      });
    }

    const attachments = await this.attachmentService.getAttachments({
      attachmentDateTo,
      attachmentDateFrom,
      filters: restFilters,
      modelId: existingCanvassSupplier.id,
      model: MODELS.CANVASS_ITEM_SUPPLIERS,
    });

    return reply.status(200).send(attachments);
  }

  async getCanvassComments(request, reply) {
    const { id: canvassId } = request.params;
    const { MODELS } = this.constants.attachment;
    const { APPROVERS, USER_TYPES } = this.constants.user;
    const { commentDateTo, commentDateFrom } = request.query;
    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    const comments = await this.commentService.getAllComments({
      model: [MODELS.CANVASS, MODELS.CANVASS_REJECT],
      modelId: existingCanvass.id,
      filters: request.query,
      commentDateTo,
      commentDateFrom,
    });

    const approverRoleList = [
      ...Object.values(APPROVERS),
      USER_TYPES.MANAGEMENT,
    ];

    /* TODO: To Refactor - once comment table supports role type and note type */
    const groupedComments = comments.data.reduce((acc, comment) => {
      const date = new Date(comment.createdAt).toISOString().split('T')[0];

      if (!acc[date]) {
        acc[date] = [];
      }

      acc[date].push({
        ...comment,
        noteType: comment.model === MODELS.CANVASS ? 'Note' : 'Disapproval',
        roleType: approverRoleList.includes(comment.userComment.role.name)
          ? 'Approver'
          : 'Requestor',
      });

      return acc;
    }, {});

    const mappedComments = {
      ...comments,
      data: Object.entries(groupedComments).map(([date, comments]) => ({
        date,
        comments,
      })),
    };

    return reply.status(200).send(mappedComments);
  }

  async getCanvassSupplierComments(request, reply) {
    const { id: canvassSupplierId } = request.params;
    const { MODELS } = this.constants.attachment;
    const { commentDateTo, commentDateFrom } = request.query;

    const existingCanvassSupplier =
      await this.canvassItemSupplierRepository.getById(
        parseInt(canvassSupplierId),
      );

    if (!existingCanvassSupplier) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass item supplier not found',
      });
    }

    const comments = await this.commentService.getAllComments({
      commentDateTo,
      commentDateFrom,
      filters: request.query,
      modelId: existingCanvassSupplier.id,
      model: MODELS.CANVASS_ITEM_SUPPLIERS,
    });

    return reply.status(200).send(comments);
  }

  async getCanvassApprovers(request, reply) {
    const { id: canvassId } = request.params;
    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    const canvassApprovers = await this.canvassService.getCanvassApprovers(
      existingCanvass.id,
    );

    return reply.status(200).send(canvassApprovers);
  }

  async approveCanvass(request, reply) {
    const { userFromToken } = request;
    const { id: canvassId } = request.params;
    const { suppliers = [], approveReason } = request.body;
    const { MODELS } = this.constants.attachment;
    const { COMMENT_TYPES, USER_TYPES } = this.constants.note;

    const transaction = await this.db.sequelize.transaction();

    try {
      const existingCanvass = await this.canvassService.getExistingCanvass(
        parseInt(canvassId),
      );

      if (approveReason) {
        await this.noteService.createNote(
          {
            model: MODELS.CANVASS,
            modelId: existingCanvass.id,
            userName: userFromToken.fullNameUser,
            userType: USER_TYPES.APPROVER,
            commentType: COMMENT_TYPES.APPROVAL,
            note: approveReason,
          },
          {
            transaction,
          },
        );
      }

      const isAllApproved = await this.canvassService.approveCanvass({
        transaction,
        existingCanvass,
        approver: userFromToken,
        canvassSuppliers: suppliers,
      });

      if (isAllApproved) {
        await this.purchaseOrderService.createPurchaseOrder({
          transaction,
          existingCanvass,
        });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'Canvass successfully approved',
    });
  }

  async addAdhocApprover(request, reply) {
    const { userFromToken } = request;
    const { approverId } = request.body;
    const { id: canvassId } = request.params;
    const { USER_TYPES } = this.constants.user;
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const transaction = await this.db.sequelize.transaction();

    try {
      const existingCanvass = await this.canvassService.getExistingCanvass(
        parseInt(canvassId),
      );

      /* Ensure that user exist with valid role at first index */
      const approverRoleList = [
        ...Object.values(this.constants.user.APPROVERS),
        USER_TYPES.PURCHASING_STAFF,
      ];
      const users = await this.userService.validateMultipleUsers([approverId], {
        roleNames: approverRoleList,
      });

      await this.canvassService.addAdhocApprover({
        transaction,
        approver: users[0],
        creatorId: userFromToken.id,
        canvassId: existingCanvass.id,
      });

      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.CANVASS,
        title: 'Assigned as an Additional Approver',
        message: `${userFromToken.firstName} ${userFromToken.lastName} has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
        recipientUserIds: [approverId],
        metaData: {
          addedBy: userFromToken.id,
          adhocApprover: approverId,
          canvassId: existingCanvass.id,
          requisitionId: existingCanvass.requisitionId,
        },
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'Canvass approver updated successfully',
    });
  }

  async rejectCanvass(request, reply) {
    const { userFromToken } = request;
    const { rejectReason } = request.body;
    const { id: canvassId } = request.params;
    const { MODELS } = this.constants.attachment;
    const { COMMENT_TYPES, USER_TYPES } = this.constants.note;
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const transaction = await this.db.sequelize.transaction();

    try {
      const existingCanvass = await this.canvassService.getExistingCanvass(
        parseInt(canvassId),
      );

      await this.canvassService.rejectCanvass({
        transaction,
        existingCanvass,
        approverId: userFromToken.id,
      });

      await this.noteService.createNote(
        {
          model: MODELS.CANVASS,
          modelId: existingCanvass.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.APPROVER,
          commentType: COMMENT_TYPES.DISAPPROVAL,
          note: rejectReason,
        },
        {
          transaction,
        },
      );

      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.CANVASS,
        title: 'Canvass Sheet Rejected',
        message:
          'Canvass Sheet has been Rejected by one of the Approvers. Click here or access the Dashboard to proceed in reviewing the Requisition Slip',
        recipientUserIds: [
          existingCanvass.requisition.assignedTo,
          existingCanvass.requisition.createdBy,
        ],
        metaData: {
          addedBy: userFromToken.id,
          rejectReason: rejectReason,
          canvassId: existingCanvass.id,
          requisitionId: existingCanvass.requisitionId,
        },
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'Canvass rejected successfully',
    });
  }

  async removeAdhocApprover(request, reply) {
    const { userFromToken } = request;
    const { id: canvassId } = request.params;
    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    await this.canvassService.removeAdhocApprover({
      canvassId: existingCanvass.id,
      primaryApproverId: userFromToken.id,
    });

    return reply.status(200).send({
      message: 'Canvass approver successfully removed',
    });
  }

  async addItemSupplierComment(request, reply) {
    const { notes, userType } = request.body;
    const { userFromToken } = request;
    const { MODELS } = this.constants.attachment;
    const { COMMENT_TYPES } = this.constants.note;

    const canvassSupplierId = parseInt(request.params.id);
    const existingCanvassSupplier =
      await this.canvassItemSupplierRepository.getById(
        parseInt(canvassSupplierId),
      );

    if (!existingCanvassSupplier) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass item supplier not found',
      });
    }

    const createdComment = await this.noteService.createNote({
      model: MODELS.CANVASS_ITEM_SUPPLIERS,
      modelId: existingCanvassSupplier.id,
      userName: userFromToken.fullNameUser,
      userType,
      commentType: COMMENT_TYPES.NOTE,
      note: notes,
    });

    return reply.status(200).send({
      message: 'Note added successfully',
      note: createdComment,
    });
  }

  async addCanvassComment(request, reply) {
    const { notes } = request.body;
    const { userFromToken } = request;
    const { MODELS } = this.constants.attachment;
    const canvassId = parseInt(request.params.id);

    const existingCanvass = await this.canvassService.getExistingCanvass(
      parseInt(canvassId),
    );

    if (!existingCanvass) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass not found',
      });
    }

    const createdComment = await this.commentService.createComment({
      comment: notes,
      model: MODELS.CANVASS,
      userId: userFromToken.id,
      modelId: existingCanvass.id,
    });

    return reply.status(200).send({
      message: 'Note added successfully',
      note: createdComment,
    });
  }

  async updateCanvassItemSupplier(request, reply) {
    const { userFromToken } = request;
    const transaction = await this.db.sequelize.transaction();
    const { id: canvassItemId, suppliers = [] } = request.body;
    const { NOTIFICATION_TYPES } = this.constants.notification;

    if (suppliers.length === 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No canvass supplier to update',
      });
    }

    try {
      const canvassItem =
        await this.canvassItemService.getCanvassItemById(canvassItemId);

      const existingSuppliers =
        await this.canvassItemSupplierRepository.findAll({
          where: { canvassItemId: canvassItem.id },
          transaction,
        });

      const { id: requisitionId, type: canvassType } =
        canvassItem.canvass.requisition;

      await this.canvassService.checkItemsValidity({
        canvassType,
        transaction,
        requisitionId,
        canvassItemDetails: {
          suppliers,
          canvassRequisitionId: canvassItem.canvassRequisitionId,
          requisitionItemListId: canvassItem.requisitionItemListId,
        },
        skipSubmissionItemCheck: true,
      });

      await this.canvassService.processSuppliers({
        suppliers,
        transaction,
        isDraft: false,
        isUpdateItems: true,
        assignedTo: userFromToken.id,
        canvassItemId: canvassItem.id,
        existingSuppliers: existingSuppliers.data,
        userFromToken,
        canvassId: canvassItem.canvassRequisitionId,
      });

      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.CANVASS,
        title: 'Supplier Canvass Updated',
        message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the List of Suppliers for the Canvass Sheet of your Request. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
        recipientUserIds: [
          canvassItem.canvass.requisition.createdBy,
          canvassItem.canvass.requisition.assignedTo,
        ],
        metaData: {
          addedBy: userFromToken.id,
          canvassId: canvassItem.canvassRequisitionId,
          requisitionId: canvassItem.canvass.requisition.id,
        },
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'Supplier updated successfully',
    });
  }

  async generatePdf(request, reply) {
    const { id } = request.params;

    try {
      const canvassId = parseInt(id);

      const data =
        await this.canvassService.generateCanvassDashboardData(canvassId);

      const result = await this.templateService.generateDynamicTemplate(
        data,
        'canvassDashboard',
        'canvass_downloads',
        'CS',
      );

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(result.pdfBytes);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Canvass;
