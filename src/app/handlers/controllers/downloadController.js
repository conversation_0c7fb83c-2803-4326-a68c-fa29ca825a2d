class Download {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      leaveService,
      fastify,
      leaveRepository,
      clientErrors,
      downloadService,
      nonRequisitionRepository,
    } = container;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.leaveService = leaveService;
    this.leaveRepository = leaveRepository;
    this.clientErrors = clientErrors;
    this.downloadService = downloadService;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.requisitionEntity = entities.requisition;
  }
  async downloadDashboardExcel(request, reply) {
    this.fastify.log.info(`Generating dashboard Excel file...`);

    const { requisitionSortSchema, requisitionFilterSchema } =
      this.requisitionEntity;

    const date = new Date();

    const utcMilliseconds =
      date.getTime() + date.getTimezoneOffset() * 60 * 1000;

    const phOffset = 8 * 60 * 60 * 1000;

    const phTime = new Date(utcMilliseconds + phOffset);

    const fileName = `RSDASHBOARD_${this.utils.convertDateYYYYMMDDHHMMSS(phTime)}`;

    const { sortBy, filterBy, ...queries } = request.query;

    const parsedSortBy = requisitionSortSchema.parse(sortBy);
    const parsedFilterBy = requisitionFilterSchema.parse(filterBy);

    try {
      const result = await this.downloadService.downloadDashboardExcel({
        sortBy: parsedSortBy,
        filterBy: parsedFilterBy,
        userFromToken: request.userFromToken,
        ...queries,
      });

      return reply.status(200).send({ fileName, data: result });
    } catch (error) {
      this.fastify.log.error(
        `Error in dashboard excel controller: ${error.message}`,
      );
      throw error;
    }
  }

  async downloadNonRSDashboard(request, reply) {
    const { userFromToken } = request;
    const { category } = request.body;
    const userId = userFromToken.id;
    let fileName = category || 'all';

    const nonRSData = await this.nonRequisitionRepository.getAllNonRs({
      userId,
      category,
      paginate: false,
    });

    if (!nonRSData.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No dashboard data to download',
      });
    }

    const downloadPayload = nonRSData.data.map((data) => {
      const {
        chargeTo,
        status,
        totalDiscountedAmount,
        updatedAt,
        requestor,
        nonRsLetter,
        draftNonRsNumber,
        nonRsNumber,
      } = data;

      const isDraft = status === 'draft';
      const nonRSNumber = isDraft
        ? `NRS-TMP-${nonRsLetter}${draftNonRsNumber}`
        : `NRS-${nonRsLetter}${nonRsNumber}`;
      const requestedBy = requestor?.fullName ?? '';

      return {
        'Non-RS Number': nonRSNumber,
        'Charge To': chargeTo,
        'Requested By': requestedBy,
        'Last Updated': updatedAt,
        Amount: totalDiscountedAmount,
        Status: status,
      };
    });

    const excelFile = this.downloadService.generateExcelFile(downloadPayload);

    return reply.status(200).send({ fileName, data: excelFile });
  }
}

module.exports = Download;
