async function downloadRoutes(fastify) {
  const downloadController = fastify.diScope.resolve('downloadController');

  fastify.route({
    method: 'GET',
    url: '/dashboard',
    handler: downloadController.downloadDashboardExcel.bind(downloadController),
  });

  fastify.route({
    method: 'POST',
    url: '/non-rs-dashboard',
    handler: downloadController.downloadNonRSDashboard.bind(downloadController),
  });
}

module.exports = downloadRoutes;
