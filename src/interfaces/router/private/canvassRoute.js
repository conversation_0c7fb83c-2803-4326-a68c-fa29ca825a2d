async function canvassRequisitionRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const canvassController = fastify.diScope.resolve('canvassController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  /* -------------------------------------------------------------------------- */
  /*                                    POST                                    */
  /* -------------------------------------------------------------------------- */

  fastify.route({
    method: 'GET',
    url: '/:requisitionId',
    schema: {
      params: entities.requisition.getRequisitionByIdParams,
    },
    handler: canvassController.getAllCanvass.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/',
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.CREATE_CANVASS)]),
      fastify.uploadFile,
    ],
    config: { timeout: 30000 },
    handler: canvassController.createCanvass.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/comment',
    schema: {
      params: entities.canvass.getCanvassParams,
      body: entities.canvass.addCommentSchema,
    },
    handler: canvassController.addCanvassComment.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/approve',
    schema: {
      params: entities.canvass.getCanvassParams,
      body: entities.canvass.approveSelectedSuppliers,
    },
    handler: canvassController.approveCanvass.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/reject',
    schema: {
      params: entities.canvass.getCanvassParams,
      body: entities.canvass.rejectCanvassSchema,
    },
    handler: canvassController.rejectCanvass.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/add-adhoc-approver',
    schema: {
      params: entities.canvass.getCanvassParams,
      body: entities.canvass.addAdhocApproverSchema,
    },
    handler: canvassController.addAdhocApprover.bind(canvassController),
  });

  fastify.route({
    method: 'POST',
    url: '/item-supplier/:id/comment',
    schema: {
      params: entities.canvass.getCanvassParams,
      body: entities.canvass.addCommentSchema,
    },
    handler: canvassController.addItemSupplierComment.bind(canvassController),
  });

  /* -------------------------------------------------------------------------- */
  /*                                    GET                                     */
  /* -------------------------------------------------------------------------- */

  fastify.route({
    method: 'GET',
    url: '/:id/items',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_CANVASS)]),
    handler: canvassController.getCanvassItems.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/attachments',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler: canvassController.getCanvassAttachments.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/comments',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler: canvassController.getCanvassComments.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/item-supplier/:id/comments',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler:
      canvassController.getCanvassSupplierComments.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/item-supplier/:id/attachment',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler:
      canvassController.getCanvassSupplierAttachments.bind(canvassController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/approvers',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler: canvassController.getCanvassApprovers.bind(canvassController),
  });

  /* -------------------------------------------------------------------------- */
  /*                                 DELETE                                     */
  /* -------------------------------------------------------------------------- */

  fastify.route({
    method: 'DELETE',
    url: '/:id/remove-adhoc-approver',
    schema: {
      params: entities.canvass.getCanvassParams,
    },
    handler: canvassController.removeAdhocApprover.bind(canvassController),
  });

  /* -------------------------------------------------------------------------- */
  /*                                 PUT                                     */
  /* -------------------------------------------------------------------------- */

  fastify.route({
    method: 'PUT',
    url: '/canvass-item-supplier',
    schema: {
      body: entities.canvass.updateItemsSchemaV2,
    },
    handler:
      canvassController.updateCanvassItemSupplier.bind(canvassController),
  });
}

module.exports = canvassRequisitionRoutes;
