async function userRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const userController = fastify.diScope.resolve('userController');

  const { permission: permissionConstants, user: userConstants } =
    fastify.diScope.resolve('constants');
  const { USER_TYPES, APPROVERS } = userConstants;
  const { PERMISSIONS } = permissionConstants;

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
    handler: userController.getUserList.bind(userController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.user.updateUserParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
    handler: userController.getUserDetails.bind(userController),
  });

  /* Token specific user retrieval */
  fastify.route({
    method: 'GET',
    url: '/me',
    handler: userController.getUserTokenDetails.bind(userController),
  });

  fastify.route({
    method: 'GET',
    url: '/roles',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_ROLES)]),
    handler: userController.getUserRoles.bind(userController),
  });

  /* Department association options - area staffs */
  fastify.route({
    method: 'GET',
    url: '/area-staffs',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.AREA_STAFF;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - approvers */
  fastify.route({
    method: 'GET',
    url: '/approvers',
    preHandler: [
      async (request) => {
        request.roleName = Object.values(APPROVERS);
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - supervisors */
  fastify.route({
    method: 'GET',
    url: '/supervisors',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.SUPERVISOR;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - managers */
  fastify.route({
    method: 'GET',
    url: '/department-heads',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.DEPARTMENT_HEAD;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - secretaries */
  fastify.route({
    method: 'GET',
    url: '/secretaries',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.DEPARTMENT_SECRETARY;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - assistant managers */
  fastify.route({
    method: 'GET',
    url: '/assistant-managers',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.ASSISTANT_MANAGER;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Department options - department division heads */
  fastify.route({
    method: 'GET',
    url: '/department-division-heads',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.DEPARTMENT_DIVISION_HEAD;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Purchasing options - purchasing heads */
  fastify.route({
    method: 'GET',
    url: '/purchasing-heads',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.PURCHASING_HEAD;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  /* Purchasing options - purchasing staffs */
  fastify.route({
    method: 'GET',
    url: '/purchasing-staffs',
    preHandler: [
      async (request) => {
        request.roleName = USER_TYPES.PURCHASING_STAFF;
        return request;
      },
    ],
    handler: userController.getUserByRole.bind(userController),
  });

  fastify.route({
    method: 'POST',
    url: '/',
    schema: {
      body: entities.user.addUserRequest,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.CREATE_USERS)]),
    handler: userController.createUser.bind(userController),
  });

  /* Granular permission logic - user update */
  fastify.route({
    method: 'PUT',
    url: '/:id',
    schema: {
      params: entities.user.updateUserParams,
      body: entities.user.updateUserRequest,
    },
    handler: userController.updateUser.bind(userController),
  });

  /* Token specific user update */
  fastify.route({
    method: 'PUT',
    url: '/update-password',
    schema: {
      body: entities.user.updatePassRequest,
    },
    handler: userController.updatePassword.bind(userController),
  });

  fastify.route({
    method: 'POST',
    url: '/reset-password',
    schema: {
      body: entities.user.resetPasswordRequest,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_USERS)]),
    handler: userController.resetPassword.bind(userController),
  });
}

module.exports = userRoutes;
