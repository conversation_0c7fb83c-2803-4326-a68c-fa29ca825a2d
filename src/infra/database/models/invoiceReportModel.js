// src/infra/database/models/invoiceModel.js
const { note: NOTE } = require('../../../domain/constants');

module.exports = (sequelize, DataTypes) => {
  const Invoice = sequelize.define(
    'invoice_reports',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      irNumber: {
        type: DataTypes.STRING,
        get() {
          const value = this.getDataValue('irNumber');
          return value === null ? null : `IR-${value}`;
        },
      },
      irDraftNumber: {
        type: DataTypes.STRING,
        get() {
          const value = this.getDataValue('irDraftNumber');
          return value === null ? null : `IR-TMP-${value}`;
        },
      },
      isDraft: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      requisitionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      purchaseOrderId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
      },
      companyCode: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      supplierInvoiceNo: {
        type: DataTypes.STRING,
      },
      issuedInvoiceDate: {
        type: DataTypes.DATE,
      },
      invoiceAmount: {
        type: DataTypes.DECIMAL(10, 2),
      },
      note: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      status: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '--',
        field: 'status',
      },
      paymentRequestId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'rs_payment_requests',
          key: 'id',
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  Invoice.associate = (models) => {
    Invoice.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    Invoice.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });

    Invoice.belongsTo(models.userModel, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
    });

    Invoice.hasMany(models.deliveryReceiptModel, {
      foreignKey: 'invoiceId',
      as: 'deliveryReceipts',
    });

    Invoice.belongsTo(models.rsPaymentRequestModel, {
      foreignKey: 'paymentRequestId',
      as: 'paymentRequest',
    });
  };

  const createInvoiceNote = async (invoice, options) => {
    if (invoice.note && !invoice.isDraft) {
      const requisition = await sequelize.model('requisitions').findOne({
        attributes: ['assignedTo', 'createdBy'],
        where: { id: invoice.requisitionId },
      });

      const createdByUser = await sequelize.model('users').findOne({
        attributes: ['id', 'firstName', 'lastName'],
        where: { id: invoice.createdBy },
      });

      const { transaction } = options;

      await sequelize.model('notes').create(
        {
          model: NOTE.MODELS.INVOICE,
          modelId: invoice.id,
          note: invoice.note,
          userName: createdByUser.fullNameUser,
          commentType: NOTE.COMMENT_TYPES.NOTE,
          userType: NOTE.USER_TYPES.REQUESTOR,
        },
        { transaction },
      );
    }
  };

  const createInvoiceReportRequestHistory = async (
    invoice,
    { transaction },
  ) => {
    await sequelize.model('invoice_report_histories').create(
      {
        invoiceReportId: invoice.id,
        requisitionId: invoice.requisitionId,
        purchaseOrderId: invoice.purchaseOrderId,
        irNumber: invoice.irNumber ? invoice.irNumber : invoice.irDraftNumber,
        supplierInvoiceNo: invoice.supplierInvoiceNo,
        issuedInvoiceDate: invoice.issuedInvoiceDate,
        invoiceAmount: invoice.invoiceAmount,
        status: invoice.status,
        updatedAt: invoice.updatedAt, // invoice last updated date
      },
      {
        transaction,
      },
    );
  };

  Invoice.beforeCreate(async (invoice) => {
    // Set status based on isDraft
    invoice.status = invoice.isDraft ? "IR Draft" : "Invoice Received";
  });

  Invoice.beforeUpdate(async (invoice) => {
    // Update status when isDraft changes
    if (invoice.changed('isDraft')) {
      invoice.status = invoice.isDraft ? "IR Draft" : "Invoice Received";
    }
  });

  Invoice.afterCreate(async (invoice, options) => {
    await createInvoiceNote(invoice, options);
    await createInvoiceReportRequestHistory(invoice, options);
  });

  Invoice.afterUpdate(async (invoice, options) => {
    if (invoice.changed('isDraft') && invoice.note && !invoice.isDraft) {
      await createInvoiceNote(invoice, options);
    }

    if (invoice.changed('status')) {
      await createInvoiceReportRequestHistory(invoice, options);
    }
  });

  return Invoice;
};
