const BaseRepository = require('./baseRepository');

class PurchaseOrderRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.purchaseOrderModel);
    this.db = db;
  }

  async getPOWithItsDeliveryReceipts(purchaseOrderId, invoiceId = null) {
    return this.getById(purchaseOrderId, {
      attributes: ['id', 'requisitionId', 'status'],
      include: [
        {
          model: this.db.deliveryReceiptModel,
          as: 'deliveryReceipts',
          where: {
            isDraft: false,
            invoiceId: {
              [this.db.Sequelize.Op.or]: [null, invoiceId]
            }
          },
          required: false
        }
      ]
    })
  }
}

module.exports = PurchaseOrderRepository;
