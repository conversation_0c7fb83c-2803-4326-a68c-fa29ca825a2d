const BaseRepository = require('./baseRepository');

class HistoryRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.historyModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAllItemsHistory(payload) {
    let whereClause = {};
    const {
      limit,
      filterBy,
      page,
      order,
      type,
      id
    } = payload;

    if (id) {
      whereClause.itemId = id;
    }

    if (filterBy?.rsNumber) {
      const searchTerm = filterBy?.rsNumber?.toUpperCase();
      whereClause[this.Sequelize.Op.or] = [
        this.Sequelize.where(
          this.Sequelize.literal(
            "CONCAT(CASE WHEN LENGTH(CAST(companies.code AS VARCHAR)) = 1 THEN CONCAT('0', companies.code) ELSE CAST(companies.code AS VARCHAR) END, rs_letter, rs_number)",
          ),
          { [this.Sequelize.Op.like]: `%${searchTerm}%` },
        ),
      ];
    }

    if (type) {
      whereClause.type = type;
    }

    const formattedOrder = [];
    for (const [field, direction] of order ?? [['updatedAt', 'DESC']]) {
      switch (field) {
        case 'project': 
          formattedOrder.push(
            [
              this.Sequelize.literal('"projects"."name"'),
              direction.toUpperCase(),
            ]
          );
          continue;
        case 'company':
          formattedOrder.push(
            [
              this.Sequelize.literal('"companies"."name"'),
              direction.toUpperCase(),
            ]
          );
          continue;
        case 'department':
          formattedOrder.push(
            [
              this.Sequelize.literal('"departments"."name"'),
              direction.toUpperCase(),
            ]
          );
          continue;
        default:
          formattedOrder.push([field, direction.toUpperCase()]);
          continue;
      }
    }

    return this.findAll({
      limit,
      page,
      order: formattedOrder,
      where: whereClause,
      attributes: [
        'id',
        [
          this.Sequelize.literal(
            "CONCAT(CASE WHEN LENGTH(CAST(companies.code AS VARCHAR)) = 1 THEN CONCAT('0', companies.code) ELSE CAST(companies.code AS VARCHAR) END, rs_letter, rs_number)",
          ),
          'rsNumber',
        ],
        'type',
        'itemId',
        [
          this.Sequelize.fn(
            'TO_CHAR',
            this.Sequelize.col('date_requested'),
            'DD Mon YYYY',
          ),
          'dateRequested',
        ],
        'quantityRequested',
        'price',
        'quantityDelivered',
        [
          this.Sequelize.fn(
            'TO_CHAR',
            this.Sequelize.col('date_delivered'),
            'DD Mon YYYY',
          ),
          'dateDelivered',
        ],
        'createdAt',
        'updatedAt',
      ],
      include: [
        {
          model: this.db.companyModel,
          as: 'companies',
          attributes: ['id', 'name', 'code']
        },
        {
          model: this.db.projectModel,
          as: 'projects',
          attributes: ['id', 'name']
        },
        {
          model: this.db.departmentModel,
          as: 'departments',
          attributes: ['id', 'name']
        },
      ],
    });
  }
}

module.exports = HistoryRepository;
